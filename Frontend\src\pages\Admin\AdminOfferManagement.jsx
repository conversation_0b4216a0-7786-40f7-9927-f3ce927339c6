import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectOffers,
  selectSelectedOffers,
  selectLoading,
  selectOfferFilters,
  setSelectedOffers,
  setOfferFilters,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchOffers,
  fetchOfferById,
  approveOffer,
  rejectOffer,
  deleteOffer,
  bulkApproveOffers,
  bulkRejectOffers,
  bulkDeleteOffers,
  getOfferStats,
} from "../../redux/slices/adminDashboardThunks";
import AdminLayout from "../../components/admin/AdminLayout";
import OfferDetailModal from "../../components/admin/OfferDetailModal";
import Table from "../../components/common/Table";
import AdminTableActions from "../../components/admin/AdminTableActions";
import AdminPagination from "../../components/admin/AdminPagination";
import "../../styles/AdminOfferManagement.css";
import { IMAGE_BASE_URL } from "../../utils/constants";
import { showSuccess, showError } from "../../utils/toast";

// Icons
import { FaSearch, FaFilter, FaEye, FaCheck, FaTimes, FaTrash, FaDollarSign } from "react-icons/fa";

const AdminOfferManagement = () => {
  const dispatch = useDispatch();
  const offers = useSelector(selectOffers);
  const selectedOffers = useSelector(selectSelectedOffers);
  const loading = useSelector(selectLoading);
  const filters = useSelector(selectOfferFilters);

  // Use API data if available, otherwise fall back to empty array
  const displayOffers = offers?.data || [];

  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [statusFilter, setStatusFilter] = useState(filters.status || "all");
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState(null);

  // Handle page change
  const handlePageChange = (newPage) => {
    dispatch(fetchOffers({
      page: newPage,
      limit: offers?.pagination?.limit || 10,
      search: searchTerm,
      status: statusFilter !== 'all' ? statusFilter : '',
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }));
  };

  // Initial fetch on component mount
  useEffect(() => {
    dispatch(fetchOffers({
      page: 1,
      limit: 10,
      search: '',
      status: '',
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }));
    dispatch(getOfferStats());
  }, [dispatch]);

  // Handle search and filter changes
  useEffect(() => {
    const timer = setTimeout(() => {
      dispatch(setOfferFilters({
        search: searchTerm,
        status: statusFilter
      }));
      dispatch(fetchOffers({
        page: 1,
        limit: 10,
        search: searchTerm,
        status: statusFilter !== 'all' ? statusFilter : '',
        sortBy: 'createdAt',
        sortOrder: 'desc'
      }));
    }, 500);

    return () => clearTimeout(timer);
  }, [dispatch, searchTerm, statusFilter]);

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(setSelectedOffers(displayOffers.map(offer => offer._id)));
    } else {
      dispatch(setSelectedOffers([]));
    }
  };

  // Handle individual select
  const handleSelectOffer = (offerId) => {
    const newSelection = selectedOffers.includes(offerId)
      ? selectedOffers.filter(id => id !== offerId)
      : [...selectedOffers, offerId];
    dispatch(setSelectedOffers(newSelection));
  };

  // Handle view offer details
  const handleViewOfferDetails = async (offer) => {
    try {
      const response = await dispatch(fetchOfferById(offer._id)).unwrap();
      setSelectedOffer(response);
      setShowDetailModal(true);
    } catch (error) {
      console.error('Error fetching offer details:', error);
      showError('Failed to fetch offer details');
    }
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowDetailModal(false);
    setSelectedOffer(null);
  };

  // Handle offer actions
  const handleOfferAction = async (offerItem, action) => {
    const offerId = offerItem._id;
    const contentTitle = offerItem.content?.title || 'Unknown Content';
    let rejectionReason;

    switch (action) {
      case 'view':
        handleViewOfferDetails(offerItem);
        break;
      case 'approve':
        if (window.confirm(`Approve this offer?`)) {
          try {
            await dispatch(approveOffer({ id: offerId, approvalNotes: '' })).unwrap();
            dispatch(addActivity({
              type: 'offer_approval',
              description: `Offer approved for ${contentTitle}`,
              timestamp: new Date().toISOString(),
            }));
            showSuccess('Offer has been approved!');
            dispatch(fetchOffers()); // Refresh the list
          } catch (err) {
            showError(`Failed to approve offer: ${err}`);
          }
        }
        break;
      case 'reject':
        rejectionReason = prompt('Enter rejection reason:');
        if (rejectionReason) {
          try {
            await dispatch(rejectOffer({ id: offerId, reason: rejectionReason })).unwrap();
            dispatch(addActivity({
              type: 'offer_rejection',
              description: `Offer rejected for ${contentTitle}`,
              timestamp: new Date().toISOString(),
            }));
            showSuccess('Offer has been rejected');
            dispatch(fetchOffers()); // Refresh the list
          } catch (err) {
            showError(`Failed to reject offer: ${err}`);
          }
        }
        break;
      case 'delete':
        if (window.confirm('Are you sure you want to delete this offer?')) {
          try {
            await dispatch(deleteOffer(offerId)).unwrap();
            dispatch(addActivity({
              type: 'offer_deletion',
              description: `Offer deleted for ${contentTitle}`,
              timestamp: new Date().toISOString(),
            }));
            showSuccess('Offer has been deleted');
            dispatch(fetchOffers()); // Refresh the list
          } catch (err) {
            showError(`Failed to delete offer: ${err}`);
          }
        }
        break;
      default:
        break;
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action) => {
    if (selectedOffers.length === 0) {
      showError('Please select offers first');
      return;
    }

    let reason;

    switch (action) {
      case 'approve':
        if (window.confirm(`Approve ${selectedOffers.length} selected offers?`)) {
          try {
            await dispatch(bulkApproveOffers({ offerIds: selectedOffers, approvalNotes: '' })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_offer_approval',
              description: `Bulk approved ${selectedOffers.length} offers`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            showSuccess(`${selectedOffers.length} offers approved`);
            dispatch(setSelectedOffers([]));
          } catch (error) {
            showError(`Failed to approve offers: ${error}`);
          }
        }
        break;
      case 'reject':
        reason = prompt(`Reason for rejecting ${selectedOffers.length} offers:`);
        if (reason) {
          try {
            await dispatch(bulkRejectOffers({ offerIds: selectedOffers, reason })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_offer_rejection',
              description: `Bulk rejected ${selectedOffers.length} offers - Reason: ${reason}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            showSuccess(`${selectedOffers.length} offers rejected`);
            dispatch(setSelectedOffers([]));
          } catch (error) {
            showError(`Failed to reject offers: ${error}`);
          }
        }
        break;
      case 'delete':
        if (window.confirm(`Delete ${selectedOffers.length} selected offers? This action cannot be undone.`)) {
          try {
            await dispatch(bulkDeleteOffers(selectedOffers)).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_offer_deletion',
              description: `Bulk deleted ${selectedOffers.length} offers`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            showSuccess(`${selectedOffers.length} offers deleted`);
            dispatch(setSelectedOffers([]));
          } catch (error) {
            showError(`Failed to delete offers: ${error}`);
          }
        }
        break;
      default:
        break;
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Table configuration
  const tableColumns = [
    // {
    //   key: 'select',
    //   label: (
    //     <input
    //       type="checkbox"
    //       checked={selectedOffers.length === displayOffers.length && displayOffers.length > 0}
    //       onChange={handleSelectAll}
    //     />
    //   ),
    //   render: (offer) => (
    //     <input
    //       type="checkbox"
    //       checked={selectedOffers.includes(offer._id)}
    //       onChange={() => handleSelectOffer(offer._id)}
    //     />
    //   ),
    //   className: 'select-column'
    // },
    {
      key: 'content',
      label: 'Content',
      render: (offer) => (
        <div className="content-info">
          <div className="content-thumbnail">
            {offer.content?.thumbnailUrl && (
              <img
                src={`${IMAGE_BASE_URL}${offer.content.thumbnailUrl}`}
                alt={offer.content?.title || 'Content'}
              />
            )}
          </div>
          <div className="content-details">
            <span className="content-title">{offer.content?.title || 'Unknown Content'}</span>
          </div>
        </div>
      )
    },
    {
      key: 'buyer',
      label: 'Buyer',
      render: (offer) => (
        <div className="user-info">
          <span className="name">{offer.buyer?.fullName || 'Unknown User'}</span>
          <span className="email">({offer.buyer?.email || 'No email'})</span>
        </div>
      )
    },
    {
      key: 'seller',
      label: 'Seller',
      render: (offer) => (
        <div className="user-info flex gap-10">
          <span className="name">{offer.seller?.fullName || 'Unknown User'}</span>
          <span className="email">({offer.seller?.email || 'No email'})</span>
        </div>
      )
    },
    {
      key: 'amount',
      label: 'Amount',
      render: (offer) => formatCurrency(offer.amount || 0)
    },
    {
      key: 'status',
      label: 'Status',
      render: (offer) => (
        <span className={`status-badge ${(offer.status || 'unknown').toLowerCase()}`}>
          {offer.status || 'Unknown'}
        </span>
      )
    },
    {
      key: 'message',
      label: 'Message',
      render: (offer) => (
        <div className="message-cell" title={offer.message || 'No message'}>
          {(offer.message || 'No message').length > 30
            ? `${(offer.message || 'No message').substring(0, 30)}...`
            : (offer.message || 'No message')}
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'Created',
      render: (offer) => formatDate(offer.createdAt)
    },
    {
      key: 'expiresAt',
      label: 'Expires',
      render: (offer) => formatDate(offer.expiresAt)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (offer) => (
        <AdminTableActions
          item={offer}
          onView={() => handleViewOfferDetails(offer)}
          permissions={{
            view: true,
            edit: false,
            delete: false
          }}
          tooltips={{
            view: 'View Offer Details'
          }}
        />
      ),
      className: 'actions-column'
    }
  ];

  return (
    <AdminLayout>
      <div className="admin-offer-management">

        {/* Search and Filter Section */}
        <div className="controls-section">
          <div className="search-box">
            <FaSearch />
            <input
              type="text"
              placeholder="Search offers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="filter-box">

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="Pending">Pending</option>
              <option value="Accepted">Accepted</option>
              <option value="Rejected">Rejected</option>
              <option value="Expired">Expired</option>
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedOffers.length > 0 && (
          <div className="bulk-actions">
            <span>{selectedOffers.length} selected</span>
            <button onClick={() => handleBulkAction('approve')} className="approve" title="Approve Selected">
              <FaCheck />
            </button>
            <button onClick={() => handleBulkAction('reject')} className="reject" title="Reject Selected">
              <FaTimes />
            </button>
            <button onClick={() => handleBulkAction('delete')} className="delete" title="Delete Selected">
              <FaTrash />
            </button>
          </div>
        )}

        {/* Offers Table */}
        <Table
          columns={tableColumns}
          data={displayOffers}
          isAdmin={true}
          loading={{
            isLoading: loading.offers,
            message: "Loading offers..."
          }}
          emptyMessage="No offers found"
          className="offers-table"
        />

        {/* Pagination */}
        <AdminPagination
          currentPage={offers?.pagination?.current || 1}
          totalPages={offers?.pagination?.pages || 1}
          totalItems={offers?.pagination?.total || 0}
          itemsPerPage={offers?.pagination?.limit || 10}
          onPageChange={handlePageChange}
          isLoading={loading.offers}
          className="admin-offers-pagination"
        />
      </div>

      {/* Offer Detail Modal */}
      {showDetailModal && selectedOffer && (
        <OfferDetailModal
          offer={selectedOffer}
          onClose={handleCloseModal}
          onApprove={() => handleOfferAction(selectedOffer, 'approve')}
          onReject={() => handleOfferAction(selectedOffer, 'reject')}
          onDelete={() => handleOfferAction(selectedOffer, 'delete')}
        />
      )}
    </AdminLayout>
  );
};

export default AdminOfferManagement;