import React from 'react';
import { useSelector } from 'react-redux';
import {
  selectMaintenanceMode,
  selectLaunchDateTime,
  selectIsRestricted,
  selectRestrictionReason
} from '../redux/slices/settingsSlice';
import MaintenancePage from './MaintenancePage';
import LaunchCountdownPage from './LaunchCountdownPage';

/**
 * LaunchStatusPage Component
 * Router component that determines which page to show based on site status:
 * - MaintenancePage: When maintenance mode is enabled
 * - LaunchCountdown: When launch date is set and in the future
 * - Redirects to home: When site is not restricted
 */
const LaunchStatusPage = () => {
  const maintenanceMode = useSelector(selectMaintenanceMode);
  const launchDateTime = useSelector(selectLaunchDateTime);
  const isRestricted = useSelector(selectIsRestricted);
  const restrictionReason = useSelector(selectRestrictionReason);

  // Debug logging
  console.log('LaunchStatusPage Debug:', {
    maintenanceMode,
    launchDateTime,
    isRestricted,
    restrictionReason
  });

  // If not restricted, redirect to home (this shouldn't happen due to route guards)
  if (!isRestricted) {
    window.location.href = '/';
    return null;
  }

  // Show maintenance page if maintenance mode is enabled
  if (maintenanceMode) {
    return <MaintenancePage />;
  }

  // Show countdown page if launch date is set and in the future
  if (launchDateTime && restrictionReason === 'pre-launch') {
    return <LaunchCountdownPage />;
  }

  // Fallback to countdown page
  return <LaunchCountdownPage />;
};

export default LaunchStatusPage;
